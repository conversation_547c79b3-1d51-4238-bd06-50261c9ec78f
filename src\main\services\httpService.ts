import express from 'express'
import cors from 'cors'
import { Server } from 'http'
import bodyParser from 'body-parser'
import { BrowserWindow } from 'electron'

// 存储最新的图像数据
let latestImageData = {
  code: 0,
  id: '',
  img: '',
  timestamp: Date.now(),
  gender: 1,
  age:16
}

// 创建 Express 应用
const app = express()
let server: Server | null = null

// 配置中间件
app.use(cors())
app.use(bodyParser.json({ limit: '50mb' }))
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }))

// 初始化 HTTP 服务
function initHttpService(): void {
  // 获取最新图像数据的接口
  app.get('/getImg', (req, res) => {
    res.json(latestImageData)
  })

  // 直接返回图片数据的测试接口
  app.get('/getImgTest', (req, res) => {
    if (!latestImageData.img) {
      return res.status(404).json({
        success: false,
        message: '没有可用的图片数据'
      })
    }
    
    // 从 Base64 字符串中提取实际的图片数据
    const base64Data = latestImageData.img.replace(/^data:image\/\w+;base64,/, '')
    const imageBuffer = Buffer.from(base64Data, 'base64')
    
    // 设置响应头
    res.set('Content-Type', 'image/jpeg') // 根据实际图片类型调整
    res.set('Content-Length', imageBuffer.length.toString())
    
    // 发送图片数据
    res.send(imageBuffer)
  })

  // 添加设置状态的接口
  app.post('/setStatus', (req, res) => {
    const { status } = req.body
    
    if (!status) {
      return res.status(400).json({ 
        success: false, 
        message: '缺少状态参数' 
      })
    }
    
    // 向渲染进程发送状态更新事件
    const mainWindow = BrowserWindow.getAllWindows()[0]
    if (mainWindow) {
      mainWindow.webContents.send('status-update', status)
      console.log(`状态已更新为: ${status}`)
      
      return res.json({ 
        success: true, 
        message: `状态已更新为: ${status}`,
        timestamp: new Date().toISOString()
      })
    } else {
      return res.status(500).json({ 
        success: false, 
        message: '主窗口未找到' 
      })
    }
  })

  // 健康检查接口
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() })
  })

  // 启动服务器
  const PORT = 3030
  server = app.listen(PORT, () => {
    console.log(`HTTP 服务器已启动，监听端口 ${PORT}`)
  })

  // 错误处理
  server.on('error', (err) => {
    console.error(`HTTP 服务器错误: ${err.message}`)
  })
}

// 更新图像数据
function updateImageData(id :string, time: string, img: string, gender:number, age:number): void {
  latestImageData = {
    code: 0,
    id,
    img,
    timestamp: Date.now(),
    gender,
    age
  }
  console.log(`图像数据已更新，时间戳: ${time}`)
}

// 关闭 HTTP 服务
function closeHttpService(): void {
  if (server) {
    server.close(() => {
      console.log('HTTP 服务器已关闭')
    })
    server = null
  }
}

export default {
  initHttpService,
  updateImageData,
  closeHttpService
}