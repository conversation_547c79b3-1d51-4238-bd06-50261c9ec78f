import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import udpService from './services/udpService'
import httpService from './services/httpService'  // 导入 HTTP 服务
import fs from 'fs'
import path from 'path'
// 删除重复的导入
// import { app, ipcMain } from 'electron'

app.commandLine.appendSwitch('ignore-certificate-errors')
let isTest = import.meta.env.MODE === 'test'
function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 1080,
    height: 1080,
    x:isTest?0:200,
    y:0,
    frame: false, // 隐藏默认标题栏
    show: false,
    autoHideMenuBar: true,
    alwaysOnTop: isTest,
    fullscreen: isTest,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      allowRunningInsecureContent: true,
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })
  // 窗口创建后扫描音频文件并发送到渲染进程
  mainWindow.webContents.on('did-finish-load', () => {
    const audioFiles = scanAudioFiles()
    mainWindow.webContents.send('audio-files', audioFiles)
  })
  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  udpService.initUdpServices()
  httpService.initHttpService()  // 初始化 HTTP 服务

  // 注册 IPC 处理程序
  ipcMain.handle('greet', async (_, { id, time, img,gender,age }) => {
    try {
      // 更新 HTTP 服务中的图像数据
      httpService.updateImageData(id,time, img,gender,age)
      return { success: true, message: '图像数据已发送到 HTTP 服务' }
    } catch (error) {
      console.error('处理图像数据失败:', error)
      throw new Error(`处理失败: ${error.message}`)
    }
  })
  ipcMain.handle('send_udp_message', async (_, { message }) => {
    try {
      const result = await udpService.sendUdpMessage(message)
      return result
    } catch (error) {
      console.error('UDP message handler error:', error)
      throw error
    }
  })

  ipcMain.handle('log_capture_message', async (_, { message }) => {
    try {
      const result = await udpService.logCaptureImage(message)
      return result
    } catch (error) {
      console.error('Log capture message handler error:', error)
      throw error
    }
  })

  // 添加保存人脸图像的处理程序
  ipcMain.handle('save-face-image', async (_, { filename, data }) => {
    try {
      // 创建保存目录
      const saveDir = path.join(app.getPath('pictures'), 'FaceImages')
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true })
      }

      // 处理 base64 数据
      const base64Data = data.replace(/^data:image\/\w+;base64,/, '')
      const buffer = Buffer.from(base64Data, 'base64')

      // 保存文件
      const filePath = path.join(saveDir, filename)
      fs.writeFileSync(filePath, buffer)
      console.log("保存人脸图像到:", filePath);
      return { success: true, path: filePath }
    } catch (error) {
      console.error('保存人脸图像失败:', error)
      throw new Error(`保存失败: ${error.message}`)
    }
  })
  // 添加IPC处理程序，当渲染进程请求音频文件列表时响应
  ipcMain.handle('get-audio-files', async () => {
    return scanAudioFiles()
  })
  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC test
  ipcMain.on('ping', () => console.log('pong'))

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
// 添加关闭应用程序的处理程序
ipcMain.on('close-app', () => {
  app.quit()
})
// 在应用退出前关闭 HTTP 服务
app.on('will-quit', () => {
  httpService.closeHttpService()
})

// 在createWindow函数之后添加
function scanAudioFiles() {
  try {
    // 获取应用程序的资源目录路径
    const resourcePath = is.dev
      ? path.join(process.cwd(), 'src/renderer/public')
      : path.join(process.resourcesPath)
    // console.log('资源目录路径:', resourcePath)
    // audio文件夹路径
    const audioPath = path.join(resourcePath, 'audio')

    // 检查目录是否存在
    if (!fs.existsSync(audioPath)) {
      console.error('Audio目录不存在:', audioPath)
      return []
    }

    // 读取目录中的所有文件
    const files = fs.readdirSync(audioPath)

    // 过滤出音频文件（.mp3, .wav等）
    const audioFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase()
      return ['.mp3', '.wav', '.ogg'].includes(ext)
    })

    // console.log('找到音频文件:', audioFiles)
    return audioFiles
  } catch (error) {
    console.error('扫描音频文件时出错:', error)
    return []
  }
}

