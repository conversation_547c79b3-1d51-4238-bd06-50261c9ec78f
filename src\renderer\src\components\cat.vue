<script setup>
import { ref, onMounted, onUnmounted,provide } from 'vue'
import * as srs from './sdk.js'

import Lipsync from '../utils/threelipsync-master/threelipsync-master/threelipsync.js'

import * as THREE from 'three'

import Stats from 'three/addons/libs/stats.module.js'
import { GUI } from 'three/addons/libs/lil-gui.module.min.js'

import { FBXLoader } from 'three/addons/loaders/FBXLoader.js'
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js'

import { OrbitControls } from 'three/addons/controls/OrbitControls.js'

import { gsap } from 'gsap'
// import { invoke } from "@tauri-apps/api/core";
import udpman from './UdpMessage.vue'

defineProps({
  msg: String
})

let audio = null
let isPlayAudio = false
const count = ref(0)
let audioInterval = null
let closeEyeTimeout = null
let tongkongTimeout = null
// 添加显示/隐藏UI元素的状态变量
const showUI = ref(true)

// 切换UI显示状态的函数
const toggleUI = () => {
  showUI.value = !showUI.value
  let gui = document.getElementsByClassName('lil-gui')
  if (showUI.value) {
    new Array(...gui).map((ui) => {
      ui.style.display = 'block'
    })
  } else {
    new Array(...gui).map((ui) => {
      ui.style.display = 'none'
    })
  }
}
const toggleClose = () => {
  // 使用 electron 的 ipcRenderer 关闭应用程序
  window.electron.ipcRenderer.send('close-app')
}

let camera, controls, scene, renderer
let clock, gui, mixer, stats

let glb, face
// yin
const lipsync = new Lipsync()

onMounted(() => {
  console.log('on init')
  if(import.meta.env.MODE == 'test'){
    setTimeout(()=>{toggleUI()},2000)
  }
  
  init()
  //render(); // remove when using animation loop
  function init() {
    console.log('init')
    scene = new THREE.Scene()
    scene.background = new THREE.Color(0xcccccc)
    scene.fog = new THREE.FogExp2(0xcccccc, 0.002)

    let container = document.getElementById('container')
    renderer = new THREE.WebGLRenderer({ antialias: true, canvas: container })
    renderer.setPixelRatio(window.devicePixelRatio)
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setAnimationLoop(animate)

    let width = window.innerWidth
    let height = window.innerHeight
    camera = new THREE.OrthographicCamera(
      width / -2,
      width / 2,
      height / 2,
      height / -2,
      0.00001,
      1000
    )
    let offsetY = 0
    camera.position.set(0, offsetY, 50)
    // camera.lookAt(0,offsetY,0)
    camera.zoom = 112
    camera.updateProjectionMatrix()
    window.camera = camera
    // controls

    controls = new OrbitControls(camera, renderer.domElement)
    controls.listenToKeyEvents(window) // optional

    //controls.addEventListener( 'change', render ); // call this only in static scenes (i.e., if there is no animation loop)

    controls.enableDamping = true // an animation loop is required when either damping or auto-rotation are enabled
    controls.dampingFactor = 0.05
    controls.target = new THREE.Vector3(0, offsetY, 0)
    controls.screenSpacePanning = false

    controls.minDistance = 1
    controls.maxDistance = 50

    controls.maxPolarAngle = Math.PI

    clock = new THREE.Clock()
    // stats
    stats = new Stats()
    container.appendChild(stats.dom)
    // gui
    gui = new GUI()
    // lights

    const dirLight1 = new THREE.DirectionalLight(0xffffff, 3)
    dirLight1.position.set(1, 1, 1)
    scene.add(dirLight1)

    const dirLight2 = new THREE.DirectionalLight(0x002288, 3)
    dirLight2.position.set(-1, -1, -1)
    scene.add(dirLight2)

    const ambientLight = new THREE.AmbientLight(0x555555)
    scene.add(ambientLight)

    //
    let loader = new GLTFLoader()
    loader.load('/cat4.glb', (e) => {
      console.log(e)
      glb = e.scene
      glb.scale.set(50, 50, 50)
      glb.position.set(0,-2.46,0)
      let changeMaterial = (e) => {
        if (e.type == 'Mesh') {
          e.material.emissive = 0x000000
          e.material.emissiveIntensity = 1.0
          e.material.emissiveMap = e.material.map
          let metarial = new THREE.MeshBasicMaterial({
            map: e.material.map,
            side: 2
          })
          e.material = metarial
          console.log(e)
        }
      }
      let mapG = (e) => {
        e.children &&
          e.children.map((ee) => {
            changeMaterial(ee)
            mapG(ee)
          })
      }
      mapG(glb)
      window.glb = glb
      glb.children[0].children[1].material.transparent = true
      // createGUI(glb.children[0])
      for (let i of glb.children) {
        createGUI(i)
      }

      goodEye()
      tongkong()

      scene.add(e.scene)
    })
    window.addEventListener('resize', onWindowResize)
  }

  function onWindowResize() {
    let width = window.innerWidth
    let height = window.innerHeight
    camera.left = width / -2
    camera.right = width / 2
    camera.top = height / 2
    camera.bottom = height / -2
    camera.updateProjectionMatrix()
    renderer.setSize(window.innerWidth, window.innerHeight)
  }

  function animate() {
    controls.update() // only required if controls.enableDamping = true, or if controls.autoRotate = true
    const dt = clock.getDelta()

    if (mixer) mixer.update(dt)
    render()
    stats.update()
  }

  function render() {
    // idleAni()
    renderer.render(scene, camera)
  }
  function createGUI(glb, animations) {
    // mixer = new THREE.AnimationMixer(model);

    // actions = {};

    // for (let i = 0; i < animations.length; i++) {

    //   const clip = animations[i];
    //   const action = mixer.clipAction(clip);
    //   actions[clip.name] = action;

    //   if (emotes.indexOf(clip.name) >= 0 || states.indexOf(clip.name) >= 4) {

    //     action.clampWhenFinished = true;
    //     action.loop = THREE.LoopOnce;

    //   }

    // }

    // // states

    // const statesFolder = gui.addFolder('States');

    // const clipCtrl = statesFolder.add(api, 'state').options(states);

    // clipCtrl.onChange(function () {

    //   fadeToAction(api.state, 0.5);

    // });

    // statesFolder.open();

    // emotes
    let face = glb
    // 结构鼬鼬鼬鼬变了，有可能为组
    let _mesh
    
    if (face.isGroup) {
      _mesh = face.children[0]
    }else{
      _mesh = face
    }
    let morphs = _mesh.morphTargetDictionary
    const expressions = Object.keys(morphs)
    // let emotes = Object.keys(morphs)
    // const emoteFolder = gui.addFolder('Emotes');

    // function createEmoteCallback(name) {

    //   morphs[name] = function () {

    //     fadeToAction(name, 0.2);

    //     mixer.addEventListener('finished', restoreState);

    //   };

    //   emoteFolder.add(morphs, name);

    // }

    // function restoreState() {

    //   mixer.removeEventListener('finished', restoreState);

    //   fadeToAction(api.state, 0.2);

    // }

    // for (let i = 0; i < expressions.length; i++) {

    //   createEmoteCallback(expressions[i]);

    // }

    // emoteFolder.open();

    // expressions

    const expressionFolder = gui.addFolder(glb.name)

    for (let i = 0; i < expressions.length; i++) {
      expressionFolder.add(_mesh.morphTargetInfluences, i, 0, 1, 0.01).name(expressions[i])
      
    }

    // activeAction = actions['Walking'];
    // activeAction.play();

    // expressionFolder.open();
  }

  function fadeToAction(name, duration) {
    previousAction = activeAction
    activeAction = actions[name]

    if (previousAction !== activeAction) {
      previousAction.fadeOut(duration)
    }

    activeAction.reset().setEffectiveTimeScale(1).setEffectiveWeight(1).fadeIn(duration).play()
  }
})

//
onUnmounted((e) => {
  if (audioInterval) {
    clearInterval(audioInterval)
  }
  if (closeEyeTimeout) {
    clearInterval(closeEyeTimeout)
  }
  if (tongkongTimeout) {
    clearInterval(tongkongTimeout)
  }
})
let goodEye = (e) => {
  closeEyeTimeout = setTimeout(
    (e) => {
      let face = glb.children[0].children[0]
      // 添加动画过渡
      gsap.to(face.morphTargetInfluences, {
        duration: 0.045, // 动画持续时间（秒）
        // 8: 1, // morphTargetInfluences[0] 的目标值
        5: 1,
        ease: 'power2.inOut', // 缓动函数
        onComplete: () => {
          // 动画完成后，延迟一段时间再睁眼
          setTimeout(() => {
            gsap.to(face.morphTargetInfluences, {
              duration: 0.045,
              // 8: 0, // morphTargetInfluences[0] 的目标值
              5: 0,
              ease: 'power2.inOut'
            })
          }, 100) // 闭眼后等待100ms再睁眼
        }
      })
      goodEye()
    },
    Math.random() * 1000 * 10
  )
}
let tongkong = (e) => {
  tongkongTimeout = setTimeout(
    () => {
      let eyes = [glb.children[1], glb.children[2]]
      let r = Math.random() - 0.5
      let random = r / 5
      eyes.map((e, index) => {
        let is = index % 2 == 0
        let value = is ? 0 + random : -0 + random
        e.morphTargetInfluences[index ==0?0:3] = value
        console.log('seteyemorph:',value)
        // e.morphTargetInfluences[1] = value
      })
      tongkong()
    },
    Math.random() * 500 * 10
  )
}



let getSongStatus = () => isPlayAudio

let stopSong = () => {
  if (audio) {
    isPlayAudio = false
    audio.pause()
    audio.removeEventListener('ended', overSong)
    audio.remove()
    audio = null
    // 复位
    let face = glb.children[0].children[0]
    face.morphTargetInfluences.map(e=>{
      e = 0
    })
    _CatStatus.songEnd()
  }
}
let overSong = ()=>{
  stopSong()
}
let song = async (type) => {
  _CatStatus.isAwait = true
  if (isPlayAudio) {
    stopSong()
  }
  const files = await window.api?.getAudioFiles()
  switch (type) {
    case 'paizhao':
      type = 'pz'
      break
    case 'yindao':
      type = 'yd'
      break
    case 'zhaoren':
      type = 'zr'
      break
  }

  let musicURL = ''
  if (files){
    let value = files.filter(e=>e.includes(type))
    let num = Math.floor(Math.random() * value.length) 
    musicURL = `/audio/${value[num]}`
    console.log(`play: ${type}${num}.mp3`)
  }else{
    // musicURL = `/audio/${value[num]}`
  }
  
  
  if (!files){
    switch(type){
      case 'haokan':
        musicURL= '/audio/haokan.mp3'
        break
        case 'kanwo':
        musicURL= '/audio/kanwo.mp3'
        break
        case 'yiding':
        musicURL= '/audio/yiding.mp3'
        break
        case 'piaoliang':
        musicURL= '/audio/zhenpiaoliang.mp3'
        break
    }
  }
  

  // lipsync.startMic()
  lipsync.init()
  lipsync.startSample(musicURL)

  // lipsync.playSample()
  audioInterval = setInterval(() => {
    let arr = lipsync.update()
    lipsync.context.resume()
    if (arr) {
      // console.log(lipsync.update());
      // arr.map((item,index) => {
      //             vars.value.morphs[index] = item
      //         })
      let face = glb.children[0].children[0]
      face.morphTargetInfluences[2] = arr[1]
      face.morphTargetInfluences[1] = arr[0] / 2
      face.morphTargetInfluences[1] = arr[2] * 5
      face.morphTargetInfluences[4] = (-arr[1] - arr[2]) / 8
    }
  }, 16)
  function playAudio(url) {
    // 创建一个新的 audio 元素
    audio = new Audio(url)

    // 设置音频播放完成后销毁自身
    audio.addEventListener('ended', overSong)

    // 播放音频
    audio.play().catch((err) => {
      console.error('Error playing audio:', err)
    })
    isPlayAudio = true
  }

  // 示例：播放一段音频
  const audioUrl = musicURL // 替换为你的音频文件路径
  playAudio(audioUrl)
}

// 状态
class CatStatus {
  statusMap = ['idle', 'discorver', 'shooting', 'catch', 'start', 'success', 'error']
  nowStatus = 0
  tempStatus = 0
  isAwait = false
  func = null
  constructor(func) {
    this.nowStatus = 0
    this.func = func
  }
  changeStatus(status)  {
    let id = this.statusMap.findIndex(e=>e==status)
    if (id == -1) {
      return
    }
    this.tempStatus = id
    if (status === 'start') {
    }
    if (status === 'success' || status === 'error') {
      if (status == 'start') {
        status = 'idle'
      }
      return
    }
    console.log(this.tempStatus,this.nowStatus)
    // 临时缓存状态
    if (this.nowStatus == id){
      if (this.isAwait){
        return
      }
    }else if(this.nowStatus > id){
      return
    }
    this.nowStatus = id
    // 根据状态执行不同的song方法
    if (status === 'discorver') {
      song('zhaoren') // 发现人时播放"摇人"语音
    } else if (status === 'shooting') {
      song('yindao') // 拍摄时播放"引导"语音
    } else if (status === 'catch') {
      song('paizhao') // 捕获人脸时播放"拍照"语音
    }
  }
  songEnd(){
    // 如果缓存优先大于现在，立即播放缓存
    if (this.tempStatus>this.nowStatus){
      this.changeStatus(this.statusMap[this.tempStatus])
      return
    }
    this.isAwait = true
    setTimeout(() => {
      this.isAwait = false
    }, (Math.random()*10+10) * 1000)
  }
}
let _CatStatus = new CatStatus(song, getSongStatus)
// 提供changeStatus方法给子组件
const changeStatus = (status) => {
  _CatStatus.changeStatus(status)
}

// 提供changeStatus方法给子组件
provide('changeStatus', changeStatus)
</script>

<template>
  <div class="cover">
    <div></div>
  </div>
  <udpman class="udpman" :class="{ hidden: !showUI }"></udpman>
  <div id="dev">
    <button @click="song('yindao')">引导</button>
    <button @click="song('paizhao')">拍照</button>
    <button @click="song('zhaoren')">摇人</button>
  </div>
  <canvas id="container"></canvas>

  <!-- 添加右下角的显示/隐藏按钮 -->
  <button class="toggle-ui-btn" @click="toggleUI">
    {{ showUI ? '隐藏' : '显示' }}
  </button>
  <button class="toggle-ui-btn2" @click="toggleClose">
    关闭
  </button>

  <div id="vertexShader" type="x-shader/x-vertex" hidden>
    varying vec3 vWorldPosition; void main() { vec4 worldPosition = modelMatrix * vec4( position,
    1.0 ); vWorldPosition = worldPosition.xyz; gl_Position = projectionMatrix * modelViewMatrix *
    vec4( position, 1.0 ); }
  </div>

  <div id="fragmentShader" type="x-shader/x-fragment" hidden>
    uniform vec3 topColor; uniform vec3 bottomColor; uniform float offset; uniform float exponent;
    varying vec3 vWorldPosition; void main() { float h = normalize( vWorldPosition + offset ).y;
    gl_FragColor = vec4( mix( bottomColor, topColor, max( pow( max( h , 0.0), exponent ), 0.0 ) ),
    1.0 ); }
  </div>
</template>

<style scoped>
.udpman {
  position: absolute;
  left: 0;
  top: 0;
}
#dev {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
}
.read-the-docs {
  color: #888;
}
#container {
  height: 100vh;
  width: 100vw;
}

/* 添加显示/隐藏按钮的样式 */
.toggle-ui-btn {
  position: absolute;
  right: 20px;
  bottom: 20px;
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  z-index: 100;
}

.toggle-ui-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
}
.toggle-ui-btn2 {
  position: absolute;
  left: 0px;
  bottom: 0px;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  z-index: 100;
}
/* 隐藏元素的样式 */
.hidden {
  display: none !important;
}

.cover {
  height: 100vh;
  width: 100vw;
  top: 0;
  left:0;
  position: absolute;
  background: radial-gradient(circle at 50% 50%, transparent 70%, #000000 71%) no-repeat left / 100%
    100%;
  pointer-events: none;
}
/* .cover div{
  height: 100vh;
  width: 100vw;
  position: absolute;
  background: black;
  pointer-events: auto;
} */
</style>
