<script setup>
// ===== Vue 相关导入 =====
import { ref, onMounted, onUnmounted, provide } from 'vue'
import udpman from './UdpMessage.vue'

// ===== Three.js 相关导入 =====
import * as THREE from 'three'
import Stats from 'three/addons/libs/stats.module.js'
import { GUI } from 'three/addons/libs/lil-gui.module.min.js'
import { FBXLoader } from 'three/addons/loaders/FBXLoader.js'
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js'

// ===== 动画和音频相关导入 =====
import { gsap } from 'gsap'
import Lipsync from '../utils/threelipsync-master/threelipsync-master/threelipsync.js'

// ===== 其他导入 =====
import * as srs from './sdk.js'
// import { invoke } from "@tauri-apps/api/core";

// ===== 组件属性定义 =====
defineProps({
  msg: String
})

// ===== 全局变量定义 =====
// Three.js 核心对象
let camera, controls, scene, renderer
let clock, gui, mixer, stats
let glb, face

// 音频相关变量
let audio = null
let isPlayAudio = false
const lipsync = new Lipsync()

// 定时器变量
let audioInterval = null
let closeEyeTimeout = null
let tongkongTimeout = null
let idleAnimationTimeout = null

// UI 状态变量
const count = ref(0)
const showUI = ref(true)

// ===== UI 控制函数 =====
/**
 * 切换UI显示状态
 */
const toggleUI = () => {
  showUI.value = !showUI.value
  const guiElements = document.getElementsByClassName('lil-gui')
  Array.from(guiElements).forEach((ui) => {
    ui.style.display = showUI.value ? 'block' : 'none'
  })
}

/**
 * 关闭应用程序
 */
const toggleClose = () => {
  window.electron.ipcRenderer.send('close-app')
}

// ===== 组件生命周期 =====
onMounted(() => {
  console.log('Cat component initialized')

  // 测试模式下自动隐藏UI
  if (import.meta.env.MODE === 'test') {
    setTimeout(() => { toggleUI() }, 2000)
  }

  initThreeJS()
})

// ===== Three.js 初始化函数 =====
/**
 * 初始化Three.js场景、渲染器、相机等
 */
function initThreeJS() {
  console.log('Initializing Three.js scene')

  // 创建场景
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xcccccc)
  scene.fog = new THREE.FogExp2(0xcccccc, 0.002)

  // 初始化渲染器
  const container = document.getElementById('container')
  renderer = new THREE.WebGLRenderer({ antialias: true, canvas: container })
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.setSize(window.innerWidth, window.innerHeight)
  renderer.setAnimationLoop(animate)

  // 初始化相机
  setupCamera()

  // 初始化控制器
  setupControls()

  // 初始化其他组件
  setupClock()
  setupStats(container)
  setupGUI()
  setupLights()

  // 加载3D模型
  loadCatModel()

  // 监听窗口大小变化
  window.addEventListener('resize', onWindowResize)
}

/**
 * 设置相机
 */
function setupCamera() {
  const width = window.innerWidth
  const height = window.innerHeight
  const offsetY = 0

  camera = new THREE.OrthographicCamera(
    width / -2,
    width / 2,
    height / 2,
    height / -2,
    0.00001,
    1000
  )

  camera.position.set(0, offsetY, 50)
  camera.zoom = 112
  camera.updateProjectionMatrix()

  // 暴露到全局以便调试
  window.camera = camera
}

/**
 * 设置控制器
 */
function setupControls() {
  controls = new OrbitControls(camera, renderer.domElement)
  controls.listenToKeyEvents(window)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.target = new THREE.Vector3(0, 0, 0)
  controls.screenSpacePanning = false
  controls.minDistance = 1
  controls.maxDistance = 50
  controls.maxPolarAngle = Math.PI
}

/**
 * 设置时钟
 */
function setupClock() {
  clock = new THREE.Clock()
}

/**
 * 设置性能监控
 */
function setupStats(container) {
  stats = new Stats()
  container.appendChild(stats.dom)
}

/**
 * 设置GUI控制面板
 */
function setupGUI() {
  gui = new GUI()
}

/**
 * 设置场景灯光
 */
function setupLights() {
  // 主光源
  const dirLight1 = new THREE.DirectionalLight(0xffffff, 3)
  dirLight1.position.set(1, 1, 1)
  scene.add(dirLight1)

  // 辅助光源
  const dirLight2 = new THREE.DirectionalLight(0x002288, 3)
  dirLight2.position.set(-1, -1, -1)
  scene.add(dirLight2)

  // 环境光
  const ambientLight = new THREE.AmbientLight(0x555555)
  scene.add(ambientLight)
}

/**
 * 加载猫咪3D模型
 */
function loadCatModel() {
  const loader = new GLTFLoader()

  loader.load('/cat4.glb', (gltf) => {
    console.log('Cat model loaded:', gltf)

    glb = gltf.scene
    glb.scale.set(50, 50, 50)
    glb.position.set(0, -2.46, 0)

    // 处理材质
    processMaterials(glb)

    // 设置透明度
    if (glb.children[0]?.children[1]?.material) {
      glb.children[0].children[1].material.transparent = true
    }

    // 创建GUI控制面板
    glb.children.forEach(child => {
      createGUI(child)
    })

    // 启动眼部动画
    startEyeAnimations()

    // 添加到场景
    scene.add(gltf.scene)

    // 暴露到全局以便调试
    window.glb = glb
  })
}

/**
 * 递归处理模型材质
 */
function processMaterials(object) {
  const changeMaterial = (mesh) => {
    if (mesh.type === 'Mesh') {
      mesh.material.emissive = 0x000000
      mesh.material.emissiveIntensity = 1.0
      mesh.material.emissiveMap = mesh.material.map

      const material = new THREE.MeshBasicMaterial({
        map: mesh.material.map,
        side: 2
      })
      mesh.material = material
      console.log('Material processed:', mesh)
    }
  }

  const traverseAndProcess = (obj) => {
    if (obj.children) {
      obj.children.forEach(child => {
        changeMaterial(child)
        traverseAndProcess(child)
      })
    }
  }

  traverseAndProcess(object)
}

// ===== 事件处理函数 =====
/**
 * 窗口大小调整处理
 */
function onWindowResize() {
  const width = window.innerWidth
  const height = window.innerHeight

  // 更新相机投影矩阵
  camera.left = width / -2
  camera.right = width / 2
  camera.top = height / 2
  camera.bottom = height / -2
  camera.updateProjectionMatrix()

  // 更新渲染器大小
  renderer.setSize(width, height)
}

// ===== 动画循环函数 =====
/**
 * 主动画循环
 */
function animate() {
  // 更新控制器（阻尼效果需要）
  controls.update()

  // 获取时间增量
  const deltaTime = clock.getDelta()

  // 更新动画混合器
  if (mixer) {
    mixer.update(deltaTime)
  }

  // 渲染场景
  render()

  // 更新性能监控
  stats.update()
}

/**
 * 渲染函数
 */
function render() {
  renderer.render(scene, camera)
}
// ===== GUI 控制面板函数 =====
/**
 * 为模型对象创建GUI控制面板
 * @param {THREE.Object3D} modelObject - 3D模型对象
 * @param {Array} animations - 动画数组（可选）
 */
function createGUI(modelObject, animations) {
  // 获取面部网格对象
  const face = modelObject
  let mesh

  // 判断是否为组对象
  if (face.isGroup) {
    mesh = face.children[0]
  } else {
    mesh = face
  }

  // 检查是否有变形目标
  if (!mesh.morphTargetDictionary) {
    console.warn('No morph targets found for:', modelObject.name)
    return
  }

  // 获取表情列表
  const morphTargets = mesh.morphTargetDictionary
  const expressions = Object.keys(morphTargets)

  // 创建表情控制文件夹
  const expressionFolder = gui.addFolder(modelObject.name || 'Model')

  // 为每个表情创建滑块控制
  expressions.forEach((expressionName, index) => {
    expressionFolder
      .add(mesh.morphTargetInfluences, index, 0, 1, 0.01)
      .name(expressionName)
  })

  console.log(`GUI created for ${modelObject.name} with ${expressions.length} expressions`)
}

/**
 * 动画过渡函数（预留，当前未使用）
 * @param {string} name - 动画名称
 * @param {number} duration - 过渡时间
 */
function fadeToAction(name, duration) {
  // 此函数预留给未来的动画系统使用
  console.log(`Fade to action: ${name}, duration: ${duration}`)
}

// ===== 组件清理 =====
onUnmounted(() => {
  console.log('Cleaning up Cat component')

  // 清理所有定时器
  if (audioInterval) {
    clearInterval(audioInterval)
    audioInterval = null
  }
  if (closeEyeTimeout) {
    clearTimeout(closeEyeTimeout)
    closeEyeTimeout = null
  }
  if (tongkongTimeout) {
    clearTimeout(tongkongTimeout)
    tongkongTimeout = null
  }
  if (idleAnimationTimeout) {
    clearTimeout(idleAnimationTimeout)
    idleAnimationTimeout = null
  }
})

// ===== 眼部动画函数 =====
/**
 * 启动眼部动画
 */
function startEyeAnimations() {
  blinkAnimation()
  pupilMovement()
  startIdleAnimation()
}

/**
 * 眨眼动画
 */
function blinkAnimation() {
  closeEyeTimeout = setTimeout(() => {
    // 检查模型是否已加载
    if (!glb?.children[0]?.children[0]) {
      console.warn('Face model not ready for blink animation')
      blinkAnimation() // 重新尝试
      return
    }

    const face = glb.children[0].children[0]

    // 闭眼动画
    gsap.to(face.morphTargetInfluences, {
      duration: 0.045,
      5: 1, // 闭眼变形目标索引
      ease: 'power2.inOut',
      onComplete: () => {
        // 延迟后睁眼
        setTimeout(() => {
          gsap.to(face.morphTargetInfluences, {
            duration: 0.045,
            5: 0, // 恢复睁眼状态
            ease: 'power2.inOut'
          })
        }, 100) // 闭眼持续100ms
      }
    })

    // 递归调用，实现随机间隔的眨眼
    blinkAnimation()
  }, Math.random() * 10000) // 0-10秒随机间隔
}

/**
 * 瞳孔移动动画
 */
function pupilMovement() {
  tongkongTimeout = setTimeout(() => {
    // 检查眼睛模型是否已加载
    if (!glb?.children[1] || !glb?.children[2]) {
      console.warn('Eye models not ready for pupil movement')
      pupilMovement() // 重新尝试
      return
    }

    const eyes = [glb.children[1], glb.children[2]]
    const randomOffset = (Math.random() - 0.5) / 5 // 随机偏移量

    eyes.forEach((eye, index) => {
      if (!eye?.morphTargetInfluences) {
        console.warn(`Eye ${index} has no morphTargetInfluences`)
        return
      }

      const isLeftEye = index % 2 === 0
      const value = isLeftEye ? randomOffset : -randomOffset
      const morphIndex = 0 //index === 0 ? 0 : 3 // 左眼用索引0，右眼用索引3

      // 确保索引在有效范围内
      if (eye.morphTargetInfluences.length > morphIndex) {
        eye.morphTargetInfluences[morphIndex] = value
        // console.log(`Eye ${index} morph[${morphIndex}]:`, value)
      }
    })

    // 递归调用，实现持续的瞳孔移动
    pupilMovement()
  }, Math.random() * 5000) // 0-5秒随机间隔
}

/**
 * 启动空闲时的面部自然动画
 * 使用指定的6个morph目标创建自然的表情变化
 */
function startIdleAnimation() {
  idleAnimation()
}

/**
 * 空闲面部动画 - 创建自然的微表情变化
 * 使用以下morph目标：
 * 0: CAT_mouth_open - 张嘴
 * 1: CAT_mouth_open_laught - 笑容张嘴
 * 2: CAT_mouth_ange - 愤怒嘴型
 * 8: CAT_mouth_colse_L_up - 左嘴角上扬
 * 9: CAT_mouth_open_R_up - 右嘴角张开上扬
 * 10: CAT_mouth_close6 - 闭嘴状态6
 */
function idleAnimation() {
  idleAnimationTimeout = setTimeout(() => {
    // 检查面部模型是否已加载
    if (!glb?.children[0]?.children[0]) {
      console.warn('Face model not ready for idle animation')
      idleAnimation() // 重新尝试
      return
    }

    // 检查是否正在播放音频，如果是则跳过面部动画
    if (isPlayAudio) {
      idleAnimation() // 重新调度
      return
    }

    const face = glb.children[0].children[0]

    if (!face.morphTargetInfluences) {
      console.warn('Face has no morphTargetInfluences')
      idleAnimation()
      return
    }

    // 定义可用的morph目标索引和对应的动作类型
    const morphTargets = {
      mouthOpen: 0,           // CAT_mouth_open
      mouthLaugh: 1,          // CAT_mouth_open_laught
      mouthAnger: 2,          // CAT_mouth_ange
      mouthLeftUp: 8,         // CAT_mouth_colse_L_up
      mouthRightUp: 9,        // CAT_mouth_open_R_up
      mouthClose: 10          // CAT_mouth_close6
    }

    // 创建自然的表情变化
    createNaturalExpression(face, morphTargets)

    // 递归调用，创建持续的自然动画
    idleAnimation()
  }, Math.random() * 1500 + 800) // 0.8-2.3秒随机间隔，更频繁
}

// ===== 音频控制函数 =====
/**
 * 创建自然的面部表情 - 支持动作叠加
 * @param {THREE.Mesh} face - 面部网格对象
 * @param {Object} morphTargets - morph目标索引映射
 */
function createNaturalExpression(face, morphTargets) {
  // 定义基础表情动作
  const baseActions = [
    'mouth_open',        // 张嘴
    'mouth_laugh',       // 笑容
    'mouth_left_up',     // 左嘴角上扬
    'mouth_right_up',    // 右嘴角上扬
    'mouth_close',       // 闭嘴
    'mouth_slight'       // 轻微动作
  ]

  // 随机决定叠加的动作数量（2-3个）
  const actionCount = Math.floor(Math.random() * 2) + 2 // 2或3个动作

  // 随机选择要叠加的动作
  const selectedActions = []
  const availableActions = [...baseActions]

  for (let i = 0; i < actionCount; i++) {
    const randomIndex = Math.floor(Math.random() * availableActions.length)
    selectedActions.push(availableActions[randomIndex])
    availableActions.splice(randomIndex, 1) // 移除已选择的动作，避免重复
  }

  console.log(`Creating layered expression with ${actionCount} actions:`, selectedActions)

  // 执行叠加动画
  animateLayeredExpression(face, morphTargets, selectedActions)
}

/**
 * 执行叠加表情动画
 * @param {THREE.Mesh} face - 面部网格对象
 * @param {Object} morphTargets - morph目标索引映射
 * @param {Array} actions - 要叠加的动作数组
 */
function animateLayeredExpression(face, morphTargets, actions) {
  const animationValues = {}
  const duration = 0.6 + Math.random() * 0.6 // 0.6-1.2秒随机持续时间

  // 为每个动作设置morph值，允许叠加
  actions.forEach((action, index) => {
    const delay = index * 0.1 // 每个动作稍微错开时间，创造层次感

    switch (action) {
      case 'mouth_open':
        // 张嘴动作
        animationValues[morphTargets.mouthOpen] = (animationValues[morphTargets.mouthOpen] || 0) + 0.08 + Math.random() * 0.25
        break

      case 'mouth_laugh':
        // 笑容动作
        animationValues[morphTargets.mouthLaugh] = (animationValues[morphTargets.mouthLaugh] || 0) + 0.05 + Math.random() * 0.15
        break

      case 'mouth_left_up':
        // 左嘴角上扬
        animationValues[morphTargets.mouthLeftUp] = (animationValues[morphTargets.mouthLeftUp] || 0) + 0.1 + Math.random() * 0.25
        break

      case 'mouth_right_up':
        // 右嘴角上扬
        animationValues[morphTargets.mouthRightUp] = (animationValues[morphTargets.mouthRightUp] || 0) + 0.1 + Math.random() * 0.25
        break

      case 'mouth_close':
        // 闭嘴动作
        animationValues[morphTargets.mouthClose] = (animationValues[morphTargets.mouthClose] || 0) + 0.08 + Math.random() * 0.15
        break

      case 'mouth_slight':
        // 轻微随机动作
        const randomMorph = Object.values(morphTargets)[Math.floor(Math.random() * Object.values(morphTargets).length)]
        animationValues[randomMorph] = (animationValues[randomMorph] || 0) + 0.03 + Math.random() * 0.08
        break
    }
  })

  // 确保所有值不超过1.0
  Object.keys(animationValues).forEach(key => {
    animationValues[key] = Math.min(animationValues[key], 1.0)
  })

  // 执行叠加动画
  gsap.to(face.morphTargetInfluences, {
    duration: duration,
    ease: 'power2.inOut',
    ...animationValues,
    onComplete: () => {
      // 动画完成后，延迟一段时间再部分重置
      setTimeout(() => {
        if (!isPlayAudio) { // 确保不在播放音频时才重置
          partialResetFacialMorphs(face, morphTargets, animationValues)
        }
      }, 200 + Math.random() * 400) // 0.2-0.6秒后部分重置
    }
  })

  console.log(`Layered animation values:`, animationValues)
}

/**
 * 部分重置面部morph目标（保持一些残留效果）
 * @param {THREE.Mesh} face - 面部网格对象
 * @param {Object} morphTargets - morph目标索引映射
 * @param {Object} currentValues - 当前动画值
 */
function partialResetFacialMorphs(face, morphTargets, currentValues) {
  const resetValues = {}

  // 将morph目标重置到原值的20-40%，保持一些残留效果
  Object.keys(currentValues).forEach(index => {
    const retainFactor = 0.2 + Math.random() * 0.2 // 保留20-40%
    resetValues[index] = currentValues[index] * retainFactor
  })

  // 使用GSAP平滑过渡到部分重置状态
  gsap.to(face.morphTargetInfluences, {
    duration: 0.4 + Math.random() * 0.3,
    ease: 'power2.out',
    ...resetValues
  })
}

/**
 * 完全重置面部morph目标到中性状态
 * @param {THREE.Mesh} face - 面部网格对象
 * @param {Object} morphTargets - morph目标索引映射
 */
function resetFacialMorphs(face, morphTargets) {
  const resetValues = {}

  // 将所有相关morph目标重置为0
  Object.values(morphTargets).forEach(index => {
    if (index < face.morphTargetInfluences.length) {
      resetValues[index] = 0
    }
  })

  // 使用GSAP平滑过渡到重置状态
  gsap.to(face.morphTargetInfluences, {
    duration: 0.3,
    ease: 'power2.inOut',
    ...resetValues
  })
}

/**
 * 动画到指定表情
 * @param {THREE.Mesh} face - 面部网格对象
 * @param {Object} morphTargets - morph目标索引映射
 * @param {string} expressionType - 表情类型
 */
function animateToExpression(face, morphTargets, expressionType) {
  const animationValues = {}
  const duration = 0.8 + Math.random() * 0.4 // 0.8-1.2秒随机持续时间

  // 根据表情类型设置不同的morph值
  switch (expressionType) {
    case 'subtle_smile':
      // 微笑：轻微的嘴角上扬
      animationValues[morphTargets.mouthLeftUp] = 0.1 + Math.random() * 0.25
      animationValues[morphTargets.mouthRightUp] = 0.1 + Math.random() * 0.25
      animationValues[morphTargets.mouthLaugh] = 0.05 + Math.random() * 0.1
      break

    case 'slight_open':
      // 轻微张嘴：好奇或准备说话的状态
      animationValues[morphTargets.mouthOpen] = 0.08 + Math.random() * 0.25
      break

    case 'content':
      // 满足：轻微的笑容和闭嘴
      animationValues[morphTargets.mouthClose] = 0.1 + Math.random() * 0.1
      animationValues[morphTargets.mouthLeftUp] = 0.05 + Math.random() * 0.08
      animationValues[morphTargets.mouthRightUp] = 0.05 + Math.random() * 0.08
      break

    case 'curious':
      // 好奇：轻微张嘴和一侧嘴角
      animationValues[morphTargets.mouthOpen] = 0.05 + Math.random() * 0.08
      animationValues[Math.random() > 0.5 ? morphTargets.mouthLeftUp : morphTargets.mouthRightUp] = 0.08 + Math.random() * 0.1
      break

    case 'relaxed':
      // 放松：非常轻微的表情变化
      const randomMorph = Object.values(morphTargets)[Math.floor(Math.random() * Object.values(morphTargets).length)]
      animationValues[randomMorph] = 0.03 + Math.random() * 0.05
      break

    case 'neutral':
    default:
      // 中性：保持当前状态，只是轻微的变化
      const subtleMorph = Object.values(morphTargets)[Math.floor(Math.random() * Object.values(morphTargets).length)]
      animationValues[subtleMorph] = Math.random() * 0.03
      break
  }

  // 执行动画
  gsap.to(face.morphTargetInfluences, {
    duration: duration,
    ease: 'power2.inOut',
    ...animationValues,
    onComplete: () => {
      // 动画完成后，延迟一段时间再回到中性状态
      setTimeout(() => {
        if (!isPlayAudio) { // 确保不在播放音频时才重置
          resetFacialMorphs(face, morphTargets)
        }
      }, 500 + Math.random() * 1000) // 0.5-1.5秒后重置
    }
  })

  console.log(`Idle animation: ${expressionType}`, animationValues)
}

/**
 * 获取音频播放状态
 * @returns {boolean} 是否正在播放音频
 */
const getSongStatus = () => isPlayAudio

/**
 * 停止音频播放
 */
function stopSong() {
  if (!audio) return

  console.log('Stopping audio playback')

  // 停止音频
  isPlayAudio = false
  audio.pause()
  audio.removeEventListener('ended', onSongEnd)
  audio.remove()
  audio = null

  // 清理音频处理定时器
  if (audioInterval) {
    clearInterval(audioInterval)
    audioInterval = null
  }

  // 重置面部表情
  resetFacialExpressions()

  // 恢复空闲动画
  if (!idleAnimationTimeout) {
    startIdleAnimation()
  }

  // 通知状态管理器
  _CatStatus.songEnd()
}

/**
 * 音频播放结束回调
 */
function onSongEnd() {
  console.log('Audio playback ended')
  stopSong()
}

/**
 * 重置面部表情到默认状态
 */
function resetFacialExpressions() {
  if (!glb?.children[0]?.children[0]) return

  const face = glb.children[0].children[0]
  if (face.morphTargetInfluences) {
    // 将所有变形目标重置为0
    face.morphTargetInfluences.fill(0)
  }
}
/**
 * 播放语音并同步口型动画
 * @param {string} type - 语音类型
 */
async function song(type) {
  console.log(`Playing song type: ${type}`)

  // 设置等待状态
  _CatStatus.isAwait = true

  // 如果正在播放，先停止
  if (isPlayAudio) {
    stopSong()
  }

  // 获取音频文件URL
  const audioUrl = await getAudioUrl(type)
  if (!audioUrl) {
    console.warn(`No audio file found for type: ${type}`)
    return
  }

  // 初始化口型同步
  initLipSync(audioUrl)

  // 播放音频
  playAudio(audioUrl)
}

/**
 * 获取音频文件URL
 * @param {string} type - 语音类型
 * @returns {Promise<string>} 音频文件URL
 */
async function getAudioUrl(type) {
  // 类型映射
  const typeMapping = {
    'paizhao': 'pz',
    'yindao': 'yd',
    'zhaoren': 'zr'
  }

  const mappedType = typeMapping[type] || type

  // 尝试获取动态音频文件列表
  const files = await window.api?.getAudioFiles()

  if (files) {
    const matchingFiles = files.filter(file => file.includes(mappedType))
    if (matchingFiles.length > 0) {
      const randomIndex = Math.floor(Math.random() * matchingFiles.length)
      const selectedFile = matchingFiles[randomIndex]
      console.log(`Selected audio file: ${selectedFile}`)
      return `/audio/${selectedFile}`
    }
  }

  // 回退到默认音频文件
  const defaultAudioMap = {
    'haokan': '/audio/haokan.mp3',
    'kanwo': '/audio/kanwo.mp3',
    'yiding': '/audio/yiding.mp3',
    'piaoliang': '/audio/zhenpiaoliang.mp3'
  }

  return defaultAudioMap[type] || null
}

/**
 * 初始化口型同步
 * @param {string} audioUrl - 音频文件URL
 */
function initLipSync(audioUrl) {
  lipsync.init()
  lipsync.startSample(audioUrl)

  // 启动口型同步更新循环
  audioInterval = setInterval(() => {
    const lipSyncData = lipsync.update()
    lipsync.context.resume()

    if (lipSyncData && glb?.children[0]?.children[0]) {
      updateFacialAnimation(lipSyncData)
    }
  }, 16) // 约60fps
}

/**
 * 更新面部动画
 * @param {Array} lipSyncData - 口型同步数据
 */
function updateFacialAnimation(lipSyncData) {
  const face = glb.children[0].children[0]

  if (!face.morphTargetInfluences) return

  // 根据口型数据更新面部变形目标
  face.morphTargetInfluences[2] = lipSyncData[1] || 0
  face.morphTargetInfluences[1] = (lipSyncData[0] || 0) / 2
  face.morphTargetInfluences[0] = (lipSyncData[2] || 0) * 5
  face.morphTargetInfluences[4] = ((lipSyncData[1] || 0) - (lipSyncData[2] || 0)) / 8
  face.morphTargetInfluences[5] = ((lipSyncData[1] || 0) + (lipSyncData[2] || 0)) / 8
}

/**
 * 播放音频文件
 * @param {string} url - 音频文件URL
 */
function playAudio(url) {
  console.log(`Playing audio: ${url}`)

  // 创建音频元素
  audio = new Audio(url)

  // 设置播放结束回调
  audio.addEventListener('ended', onSongEnd)

  // 开始播放
  audio.play()
    .then(() => {
      isPlayAudio = true
      console.log('Audio playback started successfully')
    })
    .catch((error) => {
      console.error('Error playing audio:', error)
      isPlayAudio = false
    })
}

// ===== 状态管理类 =====
/**
 * 猫咪状态管理器
 * 负责管理猫咪的各种状态和对应的语音播放
 */
class CatStatus {
  // 状态映射表
  statusMap = ['idle', 'discover', 'shooting', 'catch', 'start', 'success', 'error']

  constructor() {
    this.nowStatus = 0      // 当前状态索引
    this.tempStatus = 0     // 临时状态索引
    this.isAwait = false    // 是否在等待状态
  }

  /**
   * 改变猫咪状态
   * @param {string} status - 新状态名称
   */
  changeStatus(status) {
    const statusIndex = this.statusMap.findIndex(s => s === status)

    // 无效状态直接返回
    if (statusIndex === -1) {
      console.warn(`Invalid status: ${status}`)
      return
    }

    this.tempStatus = statusIndex

    // 特殊状态处理
    if (status === 'success' || status === 'error') {
      console.log(`Status changed to: ${status}`)
      return
    }

    console.log(`Status transition: ${this.nowStatus} -> ${this.tempStatus}`)

    // 状态优先级检查
    if (this.nowStatus === statusIndex) {
      // 相同状态且在等待中，不执行
      if (this.isAwait) {
        return
      }
    } else if (this.nowStatus > statusIndex) {
      // 低优先级状态，不执行
      return
    }

    // 更新当前状态
    this.nowStatus = statusIndex

    // 根据状态播放对应语音
    this.playStatusAudio(status)
  }

  /**
   * 根据状态播放对应的语音
   * @param {string} status - 状态名称
   */
  playStatusAudio(status) {
    const audioMap = {
      'discover': 'zhaoren',  // 发现人时播放"摇人"语音
      'shooting': 'yindao',   // 拍摄时播放"引导"语音
      'catch': 'paizhao'      // 捕获人脸时播放"拍照"语音
    }

    const audioType = audioMap[status]
    if (audioType) {
      console.log(`Playing audio for status: ${status} -> ${audioType}`)
      song(audioType)
    }
  }

  /**
   * 语音播放结束回调
   */
  songEnd() {
    console.log('Song ended, checking for pending status changes')

    // 如果有更高优先级的缓存状态，立即执行
    if (this.tempStatus > this.nowStatus) {
      this.changeStatus(this.statusMap[this.tempStatus])
      return
    }

    // 设置等待状态，随机延迟10-20秒后解除
    this.isAwait = true
    const waitTime = (Math.random() * 10 + 10) * 1000

    setTimeout(() => {
      this.isAwait = false
      console.log('Wait state cleared')
    }, waitTime)
  }
}

// ===== 状态管理实例和导出 =====
const _CatStatus = new CatStatus()

/**
 * 改变猫咪状态的外部接口
 * @param {string} status - 状态名称
 */
const changeStatus = (status) => {
  _CatStatus.changeStatus(status)
}

// 向子组件提供状态改变方法
provide('changeStatus', changeStatus)
</script>

<template>
  <!-- 主容器 -->
  <div class="cat-container">
    <!-- 遮罩层 - 创建圆形视窗效果 -->
    <div class="cover">
      <div></div>
    </div>

    <!-- UDP消息组件 -->
    <udpman
      class="udpman"
      :class="{ hidden: !showUI }"
    />

    <!-- 开发调试按钮组 -->
    <div id="dev" class="dev-controls">
      <button @click="song('yindao')" class="dev-btn">引导</button>
      <button @click="song('paizhao')" class="dev-btn">拍照</button>
      <button @click="song('zhaoren')" class="dev-btn">摇人</button>
    </div>

    <!-- Three.js 渲染画布 -->
    <canvas id="container" class="render-canvas"></canvas>

    <!-- UI控制按钮组 -->
    <div class="control-buttons">
      <!-- 显示/隐藏UI按钮 -->
      <button class="toggle-ui-btn" @click="toggleUI">
        {{ showUI ? '隐藏' : '显示' }}
      </button>

      <!-- 关闭应用按钮 -->
      <button class="close-btn" @click="toggleClose">
        关闭
      </button>
    </div>

    <!-- 着色器代码 - 隐藏的脚本标签 -->
    <div id="vertexShader" type="x-shader/x-vertex" hidden>
      varying vec3 vWorldPosition;
      void main() {
        vec4 worldPosition = modelMatrix * vec4(position, 1.0);
        vWorldPosition = worldPosition.xyz;
        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
      }
    </div>

    <div id="fragmentShader" type="x-shader/x-fragment" hidden>
      uniform vec3 topColor;
      uniform vec3 bottomColor;
      uniform float offset;
      uniform float exponent;
      varying vec3 vWorldPosition;
      void main() {
        float h = normalize(vWorldPosition + offset).y;
        gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
      }
    </div>
  </div>
</template>

<style scoped>
/* ===== 主容器样式 ===== */
.cat-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

/* ===== 渲染画布样式 ===== */
.render-canvas {
  width: 100vw;
  height: 100vh;
  display: block;
}

/* ===== 遮罩层样式 ===== */
.cover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: radial-gradient(
    circle at 50% 50%,
    transparent 70%,
    #000000 71%
  ) no-repeat center / 100% 100%;
  pointer-events: none;
  z-index: 10;
}

/* ===== UDP消息组件样式 ===== */
.udpman {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 20;
}

/* ===== 开发调试控件样式 ===== */
.dev-controls {
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 10px;
  z-index: 30;
}

.dev-btn {
  padding: 8px 16px;
  background-color: rgba(0, 123, 255, 0.8);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.dev-btn:hover {
  background-color: rgba(0, 123, 255, 1);
}

.dev-btn:active {
  transform: translateY(1px);
}

/* ===== 控制按钮组样式 ===== */
.control-buttons {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 10px;
  z-index: 100;
}

/* UI切换按钮 */
.toggle-ui-btn {
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.toggle-ui-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 关闭按钮 */
.close-btn {
  /* position: absolute; */
  left: 20px; /* 相对于control-buttons定位 */
  bottom: 0;
  padding: 20px;
  background-color: rgba(220, 53, 69, 0.6);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.close-btn:hover {
  background-color: rgba(220, 53, 69, 0.8);
}

/* ===== 通用工具类 ===== */
.hidden {
  display: none !important;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .dev-controls {
    padding: 5px;
  }

  .dev-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .control-buttons {
    bottom: 10px;
    right: 10px;
  }

  .toggle-ui-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .close-btn {
    padding: 15px;
    font-size: 12px;
  }
}

/* ===== 可访问性增强 ===== */
.dev-btn:focus,
.toggle-ui-btn:focus,
.close-btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* ===== 动画效果 ===== */
.cat-container * {
  transition: opacity 0.3s ease;
}

.hidden {
  opacity: 0;
  pointer-events: none;
}
</style>
